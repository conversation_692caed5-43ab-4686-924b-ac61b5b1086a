<!-- 静态坐标变换发布器 -->
<launch>
    <!-- 发布从base_link到ws_scan的变换 -->
    <node pkg="tf2_ros" type="static_transform_publisher" name="base_to_lidar" 
          args="0 0 0 0 0 0 base_link ws_scan" />
    
    <!-- 发布从base_link到ws_imu的变换 -->
    <node pkg="tf2_ros" type="static_transform_publisher" name="base_to_imu" 
          args="0 0 0 0 0 0 base_link ws_imu" />
    
    <!-- 发布从ws_scan到ws_imu的变换（如果需要直接变换） -->
    <node pkg="tf2_ros" type="static_transform_publisher" name="lidar_to_imu" 
          args="0 0 0 0 0 0 ws_scan ws_imu" />
</launch>
